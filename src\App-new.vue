<template>
  <div>
    <button @click="openModal">开始模拟面试</button>
    
    <PcmMnmsModal 
      :token="token"
      :modal-title="modalTitle"
      :fullscreen="fullscreen"
      :is-open="isModalOpen"
      @modalClosed="handleModalClosed"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { PcmMnmsModal } from 'pcm-agents-vue';

const token = 'app-fc0r90cHmzmcjK2vwXRKc7pc';
const modalTitle = '模拟面试';
const fullscreen = true;
const isModalOpen = ref(false);

const openModal = () => {
  isModalOpen.value = true;
};

const handleModalClosed = () => {
  console.log('聊天窗口已关闭');
  isModalOpen.value = false;
};
</script>

<style scoped>
button {
  padding: 10px 20px;
  font-size: 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>
